<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>UnipolRental</authorName>
    <dataSourceConfig>{"dataSource":{"type":"SObject","value":{"objectApiName":"Case","fields":["Id","CaseNumber","Status","Asset.Name"],"whereClause":"Id = '{recordId}'"},"orderBy":{},"contextVariables":[{"name":"recordId","val":"5001x00000abcDEF"}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <name>urcs_TimelineIncarico</name>
    <omniUiCardType>Parent</omniUiCardType>
    <sampleDataSourceResponse>[{"Id":"5001x00000abcDEF","CaseNumber":"1-8101-2024-0912088","Status":"RIPARAZIONE RESTITUITA","Asset":{"Name":"GT831SF"}}]</sampleDataSourceResponse>
    <stylingConfiguration>{"customStyles":".timeline-container {\n  padding: 16px;\n  background: #ffffff;\n  border: 1px solid #e0e5ee;\n  border-radius: 8px;\n}\n\n.header-section {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e0e5ee;\n}\n\n.header-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.header-label {\n  font-size: 12px;\n  font-weight: 600;\n  color: #3e3e3c;\n  margin-bottom: 4px;\n}\n\n.header-value {\n  font-size: 14px;\n  font-weight: 700;\n  color: #181818;\n}\n\n.timeline-step {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24px;\n  position: relative;\n}\n\n.timeline-step:not(:last-child)::after {\n  content: '';\n  position: absolute;\n  left: 16px;\n  top: 32px;\n  width: 2px;\n  height: 24px;\n  background-color: #27ae60;\n}\n\n.step-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: #27ae60;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  flex-shrink: 0;\n}\n\n.step-icon::before {\n  content: '✓';\n  color: white;\n  font-weight: bold;\n  font-size: 16px;\n}\n\n.step-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex: 1;\n}\n\n.step-label {\n  font-size: 14px;\n  font-weight: 600;\n  color: #181818;\n}\n\n.step-date {\n  font-size: 14px;\n  font-weight: 600;\n  color: #27ae60;\n}"}</stylingConfiguration>
    <versionNumber>1</versionNumber>
</OmniUiCard>
